package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerRegistrationRequestRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerRegistrationRequestRepository interface {
	FindBuyerRegistrationRequestWithFilter(req dto.BuyerRegistrationRequestPageReqDto) ([]entity.BuyerRegistrationRequest, error)
	FindBuyerRegistrationRequestWithFilterById(id int) (*entity.BuyerRegistrationRequest, error)
	FindById(id int) (*entity.BuyerRegistrationRequest, error)
	UpdateStatusTx(tx *gorm.DB, id int, fields map[string]interface{}) (int64, error)
	FindBuyerFilesByBuyerIdsAndCategory(buyerId *int, category string) (map[int][]entity.BuyerFile, error)
	FindBuyerDirectorsByBuyerId(buyerId int) ([]entity.BuyerDirector, error)

	GetNewBuyerRegistrationRequestFlowChartIndividual() (*dto.RequestFlowChartRepoDto, error)
	GetNewBuyerRegistrationRequestFlowChartForeigner() (*dto.RequestFlowChartRepoDto, error)
	GetNewBuyerRegistrationRequestFlowChartLegalEntity() (*dto.RequestFlowChartRepoDto, error)
	GetWaitingBuyerRegistrationRequestFlowChartWaiting() (*dto.RequestFlowChartRepoDto, error)
	GetWaitingBuyerRegistrationRequestFlowChartApprove() (*dto.RequestFlowChartRepoDto, error)
	GetWaitingBuyerRegistrationRequestFlowChartReject() (*dto.RequestFlowChartRepoDto, error)
	GetEditingBuyerEditProfileRequestFlowChartWaiting() (*dto.RequestFlowChartRepoDto, error)
	GetEditingBuyerEditProfileRequestFlowChartApprove() (*dto.RequestFlowChartRepoDto, error)
	GetEditingBuyerEditProfileRequestFlowChartReject() (*dto.RequestFlowChartRepoDto, error)
	GetBudgetIncreasingCreditLimitRequestFlowChartAuto() (*dto.RequestFlowChartRepoDto, error)
	GetBudgetIncreasingCreditLimitRequestFlowChartWaiting() (*dto.RequestFlowChartRepoDto, error)
	GetBudgetIncreasingCreditLimitRequestFlowChartApprove() (*dto.RequestFlowChartRepoDto, error)
	GetBudgetIncreasingCreditLimitRequestFlowChartReject() (*dto.RequestFlowChartRepoDto, error)

	GetDB() *gorm.DB
}

func NewBuyerRegistrationRequestRepository(db *gorm.DB) BuyerRegistrationRequestRepository {
	return &buyerRegistrationRequestRepositoryImpl{DB: db}
}
