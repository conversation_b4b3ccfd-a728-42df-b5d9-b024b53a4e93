package service

import (
	"encoding/json"
	"fmt"
	"net/http"

	"backend-common-lib/constant"
	constants "backend-common-lib/constants/registration_approval_status"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"strings"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *buyerRegistrationRequest) SearchBuyerRegistrationRequestFilter(req dto.BuyerRegistrationRequestPageReqDto) (dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto], error) {
	resp := dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto]{}
	result, err := s.Repo.FindBuyerRegistrationRequestWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}
	count, err := s.Repo.CountBuyerRegistrationRequest(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	countryByCode := s.buildCountryMap()

	//NOTE - Map to DTO
	mapResult := make([]dto.BuyerRegistrationRequestDto, len(result))
	for i, v := range result {
		// Convert BuyerRegistrationRequestDetailDto to BuyerRegistrationRequestDto
		var reqAddressTypeId *int
		var docAddressTypeId *int
		var bookAddressTypeId *int
		var shipAddressTypeId *int

		// address th
		regAddr := buildRegisteredAddress(v, countryByCode)
		reqAddressTypeId = &v.RegAddressTypeID
		docAddr := buildDocumentAddress(v, countryByCode)
		docAddressTypeId = &v.DocAddressTypeID
		bookAddr := buildRegistrationBookAddress(v, countryByCode)
		bookAddressTypeId = &v.BookAddressTypeID
		shipAddr := buildShippingAddress(v, countryByCode)
		shipAddressTypeId = &v.ShipAddressTypeID

		// address en
		// note not have en some field
		regAddrEn := buildRegisteredAddressEn(v, countryByCode)
		docAddrEn := buildDocumentAddressEn(v, countryByCode)
		bookAddrEn := buildRegistrationBookAddressEn(v, countryByCode)
		shipAddrEn := buildShippingAddressEn(v, countryByCode)

		mapResult[i] = dto.BuyerRegistrationRequestDto{
			BaseDto: model.BaseDto{
				Id: v.Id,
			},
			BuyerId:                   &v.BuyerID,
			RequestDate:               v.RequestDate,
			IdIdCardFile:              v.IdIdCardFile,
			IdCardFile:                v.IdCardFile,
			IdCardFileType:            v.IdCardFileType,
			IdPermitDocFile:           v.IdPermitDocFile,
			PermitDocFileType:         v.PermitDocFileType,
			PermitDocFile:             v.PermitDocFile,
			IdBankAccountFile:         v.IdBankAccountFile,
			BankAccountFile:           v.BankAccountFile,
			BankAccountFileType:       v.BankAccountFileType,
			IdOtherAccountFile:        v.IdOtherAccountFile,
			OtherAccountFile:          v.OtherAccountFile,
			OtherAccountFileType:      v.OtherAccountFileType,
			CustomerTypeId:            v.CustomerTypeId,
			CustomerTypeCode:          v.CustomerTypeCode,
			CustomerTypeTh:            v.CustomerTypeTh,
			CustomerTypeEn:            v.CustomerTypeEn,
			NationalId:                v.IdentificationNumber,
			TitleTh:                   v.PrefixTh,
			TitleEn:                   v.PrefixEn,
			FirstNameTh:               v.FirstName,
			FirstNameEn:               v.FirstName,
			MiddleNameTh:              v.MiddleName,
			MiddleNameEn:              v.MiddleName,
			LastNameTh:                v.LastName,
			LastNameEn:                v.LastName,
			DateOfBirth:               v.DateOfBirth,
			DateOfExpiry:              v.DateOfExpiry,
			NationalityId:             v.NationalityId,
			NationalityTh:             v.NationalityTh,
			NationalityEn:             v.NationalityEn,
			NationalityCode:           v.NationalityCode,
			CompanyName:               v.CompanyName,
			CompanyRegistrationDate:   v.CompanyRegistrationDate,
			CompanyBusinessType:       v.CompanyBusinessType,
			CompanyBranchCode:         v.CompanyBranchCode,
			PhoneNumber:               v.PhoneNumber,
			Email:                     v.Email,
			RegisteredAddressTypeId:   reqAddressTypeId,
			RegisteredAddress:         regAddr,
			DocumentAddressTypeId:     docAddressTypeId,
			DocumentAddress:           docAddr,
			RegistrationBookTypeId:    bookAddressTypeId,
			RegistrationBookAddress:   bookAddr,
			ShippingAddressTypeId:     shipAddressTypeId,
			ShippingAddress:           shipAddr,
			ApprovalStatus:            (*string)(&v.ApprovalStatus),
			RejectReason:              v.Remark,
			RegisteredAddressEn:       regAddrEn,
			DocumentAddressEn:         docAddrEn,
			RegistrationBookAddressEn: bookAddrEn,
			ShippingAddressEn:         shipAddrEn,
			BankAccountNumber:         v.BankAccountNumber,
			BankName:                  v.BankName,
			AccountName:               v.AccountName,
		}

	}

	//NOTE - Response
	resp = dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto]{
		PagingModel: *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit), // แก้ total ให้เป็น count
	}

	return resp, nil
}

func (s *buyerRegistrationRequest) SearchBuyerRegistrationRequestFilterById(id int) (dto.BuyerRegistrationRequestDetailDto, error) {
	resp := dto.BuyerRegistrationRequestDetailDto{}

	// Query with the same SELECT/JOINs filtered by ID
	v, err := s.Repo.FindBuyerRegistrationRequestWithFilterById(id)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}
	// Not found handling (Raw+Scan won't return ErrRecordNotFound)
	if v == nil || v.BaseEntity == nil || v.Id == 0 {
		return resp, errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("buyer registration request with id %d not found", id), "")
	}

	b, _ := json.Marshal(v)
	fmt.Println("result json : ", string(b))

	countryByCode := s.buildCountryMap()

	// Build addresses (TH)
	var reqAddressTypeId *int
	var docAddressTypeId *int
	var bookAddressTypeId *int
	var shipAddressTypeId *int

	regAddr := buildRegisteredAddress(*v, countryByCode)
	reqAddressTypeId = &v.RegAddressTypeID
	docAddr := buildDocumentAddress(*v, countryByCode)
	docAddressTypeId = &v.DocAddressTypeID
	bookAddr := buildRegistrationBookAddress(*v, countryByCode)
	bookAddressTypeId = &v.BookAddressTypeID
	shipAddr := buildShippingAddress(*v, countryByCode)
	shipAddressTypeId = &v.ShipAddressTypeID

	// Build addresses (EN)
	regAddrEn := buildRegisteredAddressEn(*v, countryByCode)
	docAddrEn := buildDocumentAddressEn(*v, countryByCode)
	bookAddrEn := buildRegistrationBookAddressEn(*v, countryByCode)
	shipAddrEn := buildShippingAddressEn(*v, countryByCode)

	// Map to DTO
	var groupWorkPermitAndOther []dto.GroupFileWorkPermitAndOther
	var groupFileIdCard []dto.GroupFileIdCard
	var groupFileBankAccount []dto.GroupFileBankAccount
	var groupBuyerDirector []dto.GroupBuyerDirector

	// Query all buyer files by category and append to groups
	// ID_CARD
	filesIdCard, err := s.Repo.FindBuyerFilesByBuyerIdsAndCategory(v.BuyerLogID, "ID_CARD")
	if err == nil && filesIdCard != nil {
		for _, files := range filesIdCard {
			for _, file := range files {
				groupFileIdCard = append(groupFileIdCard, dto.GroupFileIdCard{
					FileCategory: file.FileCategory,
					FileId:       &file.BaseEntity.Id,
					FileName:     &file.FileName,
					FileType:     &file.FileType,
				})
			}
		}
	}
	// BANK_ACCOUNT
	filesBank, err := s.Repo.FindBuyerFilesByBuyerIdsAndCategory(v.BuyerLogID, "BANK_ACCOUNT")
	if err == nil && filesBank != nil {
		for _, files := range filesBank {
			for _, file := range files {
				groupFileBankAccount = append(groupFileBankAccount, dto.GroupFileBankAccount{
					FileCategory: file.FileCategory,
					FileId:       &file.BaseEntity.Id,
					FileName:     &file.FileName,
					FileType:     &file.FileType,
				})
			}
		}
	}
	// PERMIT_DOC
	filesPermit, err := s.Repo.FindBuyerFilesByBuyerIdsAndCategory(v.BuyerLogID, "PERMIT_DOC")
	if err == nil && filesPermit != nil {
		for _, files := range filesPermit {
			for _, file := range files {
				groupWorkPermitAndOther = append(groupWorkPermitAndOther, dto.GroupFileWorkPermitAndOther{
					FileCategory: file.FileCategory,
					FileId:       &file.BaseEntity.Id,
					FileName:     &file.FileName,
					FileType:     &file.FileType,
				})
			}
		}
	}
	// OTHER
	filesOther, err := s.Repo.FindBuyerFilesByBuyerIdsAndCategory(v.BuyerLogID, "OTHER")
	if err == nil && filesOther != nil {
		for _, files := range filesOther {
			for _, file := range files {
				groupWorkPermitAndOther = append(groupWorkPermitAndOther, dto.GroupFileWorkPermitAndOther{
					FileCategory: file.FileCategory,
					FileId:       &file.BaseEntity.Id,
					FileName:     &file.FileName,
					FileType:     &file.FileType,
				})
			}
		}
	}

	// Query all buyer directors and append to groupBuyerDirector
	directors, err := s.Repo.FindBuyerDirectorsByBuyerId(util.Val(v.BuyerLogID))
	if err == nil && directors != nil {
		for _, dir := range directors {
			groupBuyerDirector = append(groupBuyerDirector, dto.GroupBuyerDirector{
				DirectorId:                   &dir.BaseEntity.Id,
				DirectorPrefixTh:             &dir.PrefixTh,
				DirectorPrefixEn:             &dir.PrefixEn,
				DirectorFirstName:            &dir.FirstName,
				DirectorMiddleName:           &dir.MiddleName,
				DirectorLastName:             &dir.LastName,
				DirectorIdentificationNumber: &dir.IdentificationNumber,
				DirectorFileName:             &dir.FileName,
				DirectorFileType:             &dir.FileType,
				DirectorFileCategory:         &dir.FileCategory,
			})
		}
	}

	resp = dto.BuyerRegistrationRequestDetailDto{
		BaseDto:                      model.BaseDto{Id: v.Id},
		BuyerId:                      &v.BuyerID,
		RequestDate:                  v.RequestDate,
		GroupFileWorkPermitAndOthers: groupWorkPermitAndOther,
		GroupFileIdCards:             groupFileIdCard,
		GroupFileBankAccounts:        groupFileBankAccount,
		CustomerTypeId:               v.CustomerTypeId,
		CustomerTypeCode:             v.CustomerTypeCode,
		CustomerTypeTh:               v.CustomerTypeTh,
		CustomerTypeEn:               v.CustomerTypeEn,
		NationalId:                   v.IdentificationNumber,
		TitleTh:                      v.PrefixTh,
		TitleEn:                      v.PrefixEn,
		FirstNameTh:                  v.FirstName,
		FirstNameEn:                  v.FirstName,
		MiddleNameTh:                 v.MiddleName,
		MiddleNameEn:                 v.MiddleName,
		LastNameTh:                   v.LastName,
		LastNameEn:                   v.LastName,
		DateOfBirth:                  v.DateOfBirth,
		DateOfExpiry:                 v.DateOfExpiry,
		NationalityId:                v.NationalityId,
		NationalityTh:                v.NationalityTh,
		NationalityEn:                v.NationalityEn,
		NationalityCode:              v.NationalityCode,
		CompanyName:                  v.CompanyName,
		CompanyRegistrationDate:      v.CompanyRegistrationDate,
		CompanyBusinessType:          v.CompanyBusinessType,
		CompanyBranchCode:            v.CompanyBranchCode,
		PhoneNumber:                  v.PhoneNumber,
		Email:                        v.Email,
		RegisteredAddressTypeId:      reqAddressTypeId,
		RegisteredAddress:            regAddr,
		DocumentAddressTypeId:        docAddressTypeId,
		DocumentAddress:              docAddr,
		RegistrationBookTypeId:       bookAddressTypeId,
		RegistrationBookAddress:      bookAddr,
		ShippingAddressTypeId:        shipAddressTypeId,
		ShippingAddress:              shipAddr,
		ApprovalStatus:               (*string)(&v.ApprovalStatus),
		RejectReason:                 v.Remark,
		RegisteredAddressEn:          regAddrEn,
		DocumentAddressEn:            docAddrEn,
		RegistrationBookAddressEn:    bookAddrEn,
		ShippingAddressEn:            shipAddrEn,
		GroupBuyerDirectors:          groupBuyerDirector,
		BankAccountNumber:            v.BankAccountNumber,
		BankName:                     v.BankName,
		AccountName:                  v.AccountName,
	}

	return resp, nil
}

func (s *buyerRegistrationRequest) UpdateBuyerRegistrationRequestStatus(req dto.BuyerRegistrationRequestUpdateReqDto) error {
	// Normalize and validate ApprovalStatus type
	normalizedStatus, err := normalizeApprovalStatus(string(req.ApprovalStatus))
	if err != nil {
		return errs.NewBusinessError(fiber.StatusBadRequest, constant.BadRequest, err.Error(), "")
	}
	req.ApprovalStatus = normalizedStatus

	// Check if record exists and get current status
	existingRecord, err := s.Repo.FindById(req.Id)
	if err != nil {
		if err.Error() == "record not found" {
			return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("buyer registration request with id %d not found", req.Id), "")
		}
		return errs.NewError(http.StatusInternalServerError, err)
	}

	if existingRecord.ApprovalStatus != constants.RegistrationApprovalStatusWaiting {
		return errs.NewBusinessError(fiber.StatusBadRequest, constant.BadRequest, "cannot change status from approved or rejected", "")
	}

	fieldsToUpdate := map[string]interface{}{
		"approval_status": req.ApprovalStatus,
		"updated_by":      req.ActionBy,
		"updated_date":    util.Now(),
	}

	// Validate remark is required when rejecting
	if req.ApprovalStatus == constants.RegistrationApprovalStatusRejected {
		if req.Remark == nil || *req.Remark == "" {
			return errs.NewBusinessError(fiber.StatusBadRequest, constant.BadRequest, "remark is required when rejecting", "")
		}
		fieldsToUpdate["remark"] = req.Remark
	}

	latestBuyerLog, err := s.BuyerRepo.GetLatestBuyerLogByBuyerId(existingRecord.BuyerID)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	fieldsToUpdate["buyer_log_id"] = latestBuyerLog.Id

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		affectedRows, err := s.Repo.UpdateStatusTx(tx, req.Id, fieldsToUpdate)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
		}

		// เพิ่ม update ใน table buyer field is_approved = true เมื่อกรณีที่เป็น Approved
		if req.ApprovalStatus == constants.RegistrationApprovalStatusApproved {
			// Update buyer record to set is_approved = true
			buyerFieldsToUpdate := map[string]interface{}{
				"is_approved":  true,
				"updated_by":   req.ActionBy,
				"updated_date": util.Now(),
			}

			if err := s.BuyerRepo.UpdateStatusTx(tx, existingRecord.BuyerID, buyerFieldsToUpdate); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil
	})

	if errTx != nil {
		return errTx
	}

	return nil
}

// normalizeApprovalStatus converts input to uppercase and validates if it's allowed
func normalizeApprovalStatus(status string) (constants.ApprovalStatusEnum, error) {
	// Convert to uppercase
	upperStatus := strings.ToUpper(status)

	switch upperStatus {
	case "APPROVED":
		return constants.RegistrationApprovalStatusApproved, nil
	case "REJECTED":
		return constants.RegistrationApprovalStatusRejected, nil
	default:
		return "", fmt.Errorf("invalid approval status: %s. Only 'approved' or 'rejected' are allowed", status)
	}
}

// Helper function to build registered address string
func buildRegisteredAddress(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {

	// บ้านเลขที่ + ห้องเลขที่ + ชั้น + อาคาร + หมูบ้าน + หมู่ที่ + ซอย + ถนน + แขวง + เขต + จังหวัด + รหัสไปรษณีย์ + ประเทศ​

	if v.RegHouseNumber == nil && v.RegRoomNumber == nil && v.RegFloor == nil && v.RegBuilding == nil && v.RegVillage == nil && v.RegMoo == nil && v.RegSoi == nil && v.RegRoad == nil && v.RegMasterCityDescriptionTh == nil && v.RegPostCode == nil {
		return nil
	}

	var parts []string
	if v.RegHouseNumber != nil && *v.RegHouseNumber != "" {
		parts = append(parts, *v.RegHouseNumber)
	}
	if v.RegRoomNumber != nil && *v.RegRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.RegRoomNumber)
	}
	if v.RegFloor != nil && *v.RegFloor != "" {
		parts = append(parts, "ชั้น "+*v.RegFloor)
	}
	if v.RegBuilding != nil && *v.RegBuilding != "" {
		parts = append(parts, "อาคาร "+*v.RegBuilding)
	}
	if v.RegMoo != nil && *v.RegMoo != "" {
		parts = append(parts, "หมู่ "+*v.RegMoo)
	}
	if v.RegVillage != nil && *v.RegVillage != "" {
		parts = append(parts, *v.RegVillage)
	}
	if v.RegSoi != nil && *v.RegSoi != "" {
		parts = append(parts, "ซอย "+*v.RegSoi)
	}
	if v.RegRoad != nil && *v.RegRoad != "" {
		parts = append(parts, "ถนน "+*v.RegRoad)
	}
	if v.RegSubDistrictTh != nil && *v.RegSubDistrictTh != "" {
		parts = append(parts, "แขวง "+*v.RegSubDistrictTh)
	}
	if v.RegDistrictTh != nil && *v.RegDistrictTh != "" {
		parts = append(parts, "เขต "+*v.RegDistrictTh)
	}
	if v.RegMasterCityDescriptionTh != nil && *v.RegMasterCityDescriptionTh != "" {
		parts = append(parts, *v.RegMasterCityDescriptionTh)
	}
	if v.RegPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.RegPostCode))
	}
	if v.RegCountryCode != nil && *v.RegCountryCode != "" {
		code := strings.ToUpper(*v.RegCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionTh != nil && *c.DescriptionTh != "" {
				parts = append(parts, "ประเทศ "+*c.DescriptionTh)
			} else {
				parts = append(parts, "ประเทศอื่นๆ")
			}
		} else {
			parts = append(parts, "ประเทศอื่นๆ")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build document address string
func buildDocumentAddress(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.DocHouseNumber == nil && v.DocRoomNumber == nil && v.DocFloor == nil && v.DocBuilding == nil && v.DocVillage == nil && v.DocMoo == nil && v.DocSoi == nil && v.DocRoad == nil && v.DocMasterCityDescriptionTh == nil && v.DocPostCode == nil {
		return nil
	}

	var parts []string
	if v.DocHouseNumber != nil && *v.DocHouseNumber != "" {
		parts = append(parts, *v.DocHouseNumber)
	}
	if v.DocRoomNumber != nil && *v.DocRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.DocRoomNumber)
	}
	if v.DocFloor != nil && *v.DocFloor != "" {
		parts = append(parts, "ชั้น "+*v.DocFloor)
	}
	if v.DocBuilding != nil && *v.DocBuilding != "" {
		parts = append(parts, "อาคาร "+*v.DocBuilding)
	}
	if v.DocVillage != nil && *v.DocVillage != "" {
		parts = append(parts, *v.DocVillage)
	}
	if v.DocMoo != nil && *v.DocMoo != "" {
		parts = append(parts, "หมู่ "+*v.DocMoo)
	}
	if v.DocSoi != nil && *v.DocSoi != "" {
		parts = append(parts, "ซอย "+*v.DocSoi)
	}
	if v.DocRoad != nil && *v.DocRoad != "" {
		parts = append(parts, "ถนน "+*v.DocRoad)
	}
	if v.DocSubDistrictTh != nil && *v.DocSubDistrictTh != "" {
		parts = append(parts, "แขวง "+*v.DocSubDistrictTh)
	}
	if v.DocDistrictTh != nil && *v.DocDistrictTh != "" {
		parts = append(parts, "เขต "+*v.DocDistrictTh)
	}
	if v.DocMasterCityDescriptionTh != nil && *v.DocMasterCityDescriptionTh != "" {
		parts = append(parts, *v.DocMasterCityDescriptionTh)
	}
	if v.DocPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.DocPostCode))
	}
	if v.DocCountryCode != nil && *v.DocCountryCode != "" {
		code := strings.ToUpper(*v.DocCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionTh != nil && *c.DescriptionTh != "" {
				parts = append(parts, "ประเทศ "+*c.DescriptionTh)
			} else {
				parts = append(parts, "ประเทศอื่นๆ")
			}
		} else {
			parts = append(parts, "ประเทศอื่นๆ")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build registration book address string
func buildRegistrationBookAddress(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.BookHouseNumber == nil && v.BookRoomNumber == nil && v.BookFloor == nil && v.BookBuilding == nil && v.BookVillage == nil && v.BookMoo == nil && v.BookSoi == nil && v.BookRoad == nil && v.BookMasterCityDescriptionTh == nil && v.BookPostCode == nil {
		return nil
	}

	var parts []string
	if v.BookHouseNumber != nil && *v.BookHouseNumber != "" {
		parts = append(parts, *v.BookHouseNumber)
	}
	if v.BookRoomNumber != nil && *v.BookRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.BookRoomNumber)
	}
	if v.BookFloor != nil && *v.BookFloor != "" {
		parts = append(parts, "ชั้น "+*v.BookFloor)
	}
	if v.BookBuilding != nil && *v.BookBuilding != "" {
		parts = append(parts, "อาคาร "+*v.BookBuilding)
	}
	if v.BookVillage != nil && *v.BookVillage != "" {
		parts = append(parts, *v.BookVillage)
	}
	if v.BookMoo != nil && *v.BookMoo != "" {
		parts = append(parts, "หมู่ "+*v.BookMoo)
	}
	if v.BookSoi != nil && *v.BookSoi != "" {
		parts = append(parts, "ซอย "+*v.BookSoi)
	}
	if v.BookRoad != nil && *v.BookRoad != "" {
		parts = append(parts, "ถนน "+*v.BookRoad)
	}
	if v.BookSubDistrictTh != nil && *v.BookSubDistrictTh != "" {
		parts = append(parts, "แขวง "+*v.BookSubDistrictTh)
	}
	if v.BookDistrictTh != nil && *v.BookDistrictTh != "" {
		parts = append(parts, "เขต "+*v.BookDistrictTh)
	}
	if v.BookMasterCityDescriptionTh != nil && *v.BookMasterCityDescriptionTh != "" {
		parts = append(parts, *v.BookMasterCityDescriptionTh)
	}
	if v.BookPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.BookPostCode))
	}
	if v.BookCountryCode != nil && *v.BookCountryCode != "" {
		code := strings.ToUpper(*v.BookCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionTh != nil && *c.DescriptionTh != "" {
				parts = append(parts, "ประเทศ "+*c.DescriptionTh)
			} else {
				parts = append(parts, "ประเทศอื่นๆ")
			}
		} else {
			parts = append(parts, "ประเทศอื่นๆ")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

func buildShippingAddress(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.ShipHouseNumber == nil && v.ShipRoomNumber == nil && v.ShipFloor == nil && v.ShipBuilding == nil && v.ShipVillage == nil && v.ShipMoo == nil && v.ShipSoi == nil && v.ShipRoad == nil && v.ShipMasterCityDescriptionTh == nil && v.ShipPostCode == nil {
		return nil
	}

	var parts []string
	if v.ShipHouseNumber != nil && *v.ShipHouseNumber != "" {
		parts = append(parts, *v.ShipHouseNumber)
	}
	if v.ShipRoomNumber != nil && *v.ShipRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.ShipRoomNumber)
	}
	if v.ShipFloor != nil && *v.ShipFloor != "" {
		parts = append(parts, "ชั้น "+*v.ShipFloor)
	}
	if v.ShipBuilding != nil && *v.ShipBuilding != "" {
		parts = append(parts, "อาคาร "+*v.ShipBuilding)
	}
	if v.ShipVillage != nil && *v.ShipVillage != "" {
		parts = append(parts, *v.ShipVillage)
	}
	if v.ShipMoo != nil && *v.ShipMoo != "" {
		parts = append(parts, "หมู่ "+*v.ShipMoo)
	}
	if v.ShipSoi != nil && *v.ShipSoi != "" {
		parts = append(parts, "ซอย "+*v.ShipSoi)
	}
	if v.ShipRoad != nil && *v.ShipRoad != "" {
		parts = append(parts, "ถนน "+*v.ShipRoad)
	}
	if v.ShipSubDistrictTh != nil && *v.ShipSubDistrictTh != "" {
		parts = append(parts, "แขวง "+*v.ShipSubDistrictTh)
	}
	if v.ShipDistrictTh != nil && *v.ShipDistrictTh != "" {
		parts = append(parts, "เขต "+*v.ShipDistrictTh)
	}
	if v.ShipMasterCityDescriptionTh != nil && *v.ShipMasterCityDescriptionTh != "" {
		parts = append(parts, *v.ShipMasterCityDescriptionTh)
	}
	if v.ShipPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.ShipPostCode))
	}
	if v.ShipCountryCode != nil && *v.ShipCountryCode != "" {
		code := strings.ToUpper(*v.ShipCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionTh != nil && *c.DescriptionTh != "" {
				parts = append(parts, "ประเทศ "+*c.DescriptionTh)
			} else {
				parts = append(parts, "ประเทศอื่นๆ")
			}
		} else {
			parts = append(parts, "ประเทศอื่นๆ")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build registered address string (English version)
func buildRegisteredAddressEn(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.RegHouseNumber == nil && v.RegRoomNumber == nil && v.RegFloor == nil && v.RegBuilding == nil && v.RegVillage == nil && v.RegMoo == nil && v.RegSoi == nil && v.RegRoad == nil && v.RegMasterCityDescriptionEn == nil && v.RegPostCode == nil {
		return nil
	}

	var parts []string
	if v.RegHouseNumber != nil && *v.RegHouseNumber != "" {
		parts = append(parts, *v.RegHouseNumber)
	}
	if v.RegRoomNumber != nil && *v.RegRoomNumber != "" {
		parts = append(parts, "Room "+*v.RegRoomNumber)
	}
	if v.RegFloor != nil && *v.RegFloor != "" {
		parts = append(parts, "Floor "+*v.RegFloor)
	}
	if v.RegBuilding != nil && *v.RegBuilding != "" {
		parts = append(parts, *v.RegBuilding+" Building")
	}
	if v.RegMoo != nil && *v.RegMoo != "" {
		parts = append(parts, "Moo "+*v.RegMoo)
	}
	if v.RegVillage != nil && *v.RegVillage != "" {
		parts = append(parts, *v.RegVillage)
	}
	if v.RegSoi != nil && *v.RegSoi != "" {
		parts = append(parts, "Soi "+*v.RegSoi)
	}
	if v.RegRoad != nil && *v.RegRoad != "" {
		parts = append(parts, *v.RegRoad+" Road")
	}
	if v.RegSubDistrictEn != nil && *v.RegSubDistrictEn != "" {
		parts = append(parts, *v.RegSubDistrictEn+" Sub-district")
	}
	if v.RegDistrictEn != nil && *v.RegDistrictEn != "" {
		parts = append(parts, *v.RegDistrictEn+" District")
	}
	if v.RegMasterCityDescriptionEn != nil && *v.RegMasterCityDescriptionEn != "" {
		parts = append(parts, *v.RegMasterCityDescriptionEn)
	}
	if v.RegPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.RegPostCode))
	}
	if v.RegCountryCode != nil && *v.RegCountryCode != "" {
		code := strings.ToUpper(*v.RegCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionEn != nil && *c.DescriptionEn != "" {
				parts = append(parts, *c.DescriptionEn)
			} else {
				parts = append(parts, "Other Country")
			}
		} else {
			parts = append(parts, "Other Country")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build document address string (English version)
func buildDocumentAddressEn(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.DocHouseNumber == nil && v.DocRoomNumber == nil && v.DocFloor == nil && v.DocBuilding == nil && v.DocVillage == nil && v.DocMoo == nil && v.DocSoi == nil && v.DocRoad == nil && v.DocMasterCityDescriptionEn == nil && v.DocPostCode == nil {
		return nil
	}

	var parts []string
	if v.DocHouseNumber != nil && *v.DocHouseNumber != "" {
		parts = append(parts, *v.DocHouseNumber)
	}
	if v.DocRoomNumber != nil && *v.DocRoomNumber != "" {
		parts = append(parts, "Room "+*v.DocRoomNumber)
	}
	if v.DocFloor != nil && *v.DocFloor != "" {
		parts = append(parts, "Floor "+*v.DocFloor)
	}
	if v.DocBuilding != nil && *v.DocBuilding != "" {
		parts = append(parts, *v.DocBuilding+" Building")
	}
	if v.DocVillage != nil && *v.DocVillage != "" {
		parts = append(parts, *v.DocVillage)
	}
	if v.DocMoo != nil && *v.DocMoo != "" {
		parts = append(parts, "Moo "+*v.DocMoo)
	}
	if v.DocSoi != nil && *v.DocSoi != "" {
		parts = append(parts, "Soi "+*v.DocSoi)
	}
	if v.DocRoad != nil && *v.DocRoad != "" {
		parts = append(parts, *v.DocRoad+" Road")
	}
	if v.DocSubDistrictEn != nil && *v.DocSubDistrictEn != "" {
		parts = append(parts, *v.DocSubDistrictEn+" Sub-district")
	}
	if v.DocDistrictEn != nil && *v.DocDistrictEn != "" {
		parts = append(parts, *v.DocDistrictEn+" District")
	}
	if v.DocMasterCityDescriptionEn != nil && *v.DocMasterCityDescriptionEn != "" {
		parts = append(parts, *v.DocMasterCityDescriptionEn)
	}
	if v.DocPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.DocPostCode))
	}
	if v.DocCountryCode != nil && *v.DocCountryCode != "" {
		code := strings.ToUpper(*v.DocCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionEn != nil && *c.DescriptionEn != "" {
				parts = append(parts, *c.DescriptionEn)
			} else {
				parts = append(parts, "Other Country")
			}
		} else {
			parts = append(parts, "Other Country")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build registration book address string (English version)
func buildRegistrationBookAddressEn(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.BookHouseNumber == nil && v.BookRoomNumber == nil && v.BookFloor == nil && v.BookBuilding == nil && v.BookVillage == nil && v.BookMoo == nil && v.BookSoi == nil && v.BookRoad == nil && v.BookMasterCityDescriptionEn == nil && v.BookPostCode == nil {
		return nil
	}

	var parts []string
	if v.BookHouseNumber != nil && *v.BookHouseNumber != "" {
		parts = append(parts, *v.BookHouseNumber)
	}
	if v.BookRoomNumber != nil && *v.BookRoomNumber != "" {
		parts = append(parts, "Room "+*v.BookRoomNumber)
	}
	if v.BookFloor != nil && *v.BookFloor != "" {
		parts = append(parts, "Floor "+*v.BookFloor)
	}
	if v.BookBuilding != nil && *v.BookBuilding != "" {
		parts = append(parts, *v.BookBuilding+" Building")
	}
	if v.BookVillage != nil && *v.BookVillage != "" {
		parts = append(parts, *v.BookVillage)
	}
	if v.BookMoo != nil && *v.BookMoo != "" {
		parts = append(parts, "Moo "+*v.BookMoo)
	}
	if v.BookSoi != nil && *v.BookSoi != "" {
		parts = append(parts, "Soi "+*v.BookSoi)
	}
	if v.BookRoad != nil && *v.BookRoad != "" {
		parts = append(parts, *v.BookRoad+" Road")
	}
	if v.BookSubDistrictEn != nil && *v.BookSubDistrictEn != "" {
		parts = append(parts, *v.BookSubDistrictEn+" Sub-district")
	}
	if v.BookDistrictEn != nil && *v.BookDistrictEn != "" {
		parts = append(parts, *v.BookDistrictEn+" District")
	}
	if v.BookMasterCityDescriptionEn != nil && *v.BookMasterCityDescriptionEn != "" {
		parts = append(parts, *v.BookMasterCityDescriptionEn)
	}
	if v.BookPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.BookPostCode))
	}
	if v.BookCountryCode != nil && *v.BookCountryCode != "" {
		code := strings.ToUpper(*v.BookCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionEn != nil && *c.DescriptionEn != "" {
				parts = append(parts, *c.DescriptionEn)
			} else {
				parts = append(parts, "Other Country")
			}
		} else {
			parts = append(parts, "Other Country")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build shipping address string (English version)
func buildShippingAddressEn(v entity.BuyerRegistrationRequest, countryByCode map[string]entity.MasterCountry) *string {
	if v.ShipHouseNumber == nil && v.ShipRoomNumber == nil && v.ShipFloor == nil && v.ShipBuilding == nil && v.ShipVillage == nil && v.ShipMoo == nil && v.ShipSoi == nil && v.ShipRoad == nil && v.ShipMasterCityDescriptionEn == nil && v.ShipPostCode == nil {
		return nil
	}

	var parts []string
	if v.ShipHouseNumber != nil && *v.ShipHouseNumber != "" {
		parts = append(parts, *v.ShipHouseNumber)
	}
	if v.ShipRoomNumber != nil && *v.ShipRoomNumber != "" {
		parts = append(parts, "Room "+*v.ShipRoomNumber)
	}
	if v.ShipFloor != nil && *v.ShipFloor != "" {
		parts = append(parts, "Floor "+*v.ShipFloor)
	}
	if v.ShipBuilding != nil && *v.ShipBuilding != "" {
		parts = append(parts, *v.ShipBuilding+" Building")
	}
	if v.ShipVillage != nil && *v.ShipVillage != "" {
		parts = append(parts, *v.ShipVillage)
	}
	if v.ShipMoo != nil && *v.ShipMoo != "" {
		parts = append(parts, "Moo "+*v.ShipMoo)
	}
	if v.ShipSoi != nil && *v.ShipSoi != "" {
		parts = append(parts, "Soi "+*v.ShipSoi)
	}
	if v.ShipRoad != nil && *v.ShipRoad != "" {
		parts = append(parts, *v.ShipRoad+" Road")
	}
	if v.ShipSubDistrictEn != nil && *v.ShipSubDistrictEn != "" {
		parts = append(parts, *v.ShipSubDistrictEn+" Sub-district")
	}
	if v.ShipDistrictEn != nil && *v.ShipDistrictEn != "" {
		parts = append(parts, *v.ShipDistrictEn+" District")
	}
	if v.ShipMasterCityDescriptionEn != nil && *v.ShipMasterCityDescriptionEn != "" {
		parts = append(parts, *v.ShipMasterCityDescriptionEn)
	}
	if v.ShipPostCode != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.ShipPostCode))
	}
	if v.ShipCountryCode != nil && *v.ShipCountryCode != "" {
		code := strings.ToUpper(*v.ShipCountryCode)
		if c, ok := countryByCode[code]; ok {
			if c.DescriptionEn != nil && *c.DescriptionEn != "" {
				parts = append(parts, *c.DescriptionEn)
			} else {
				parts = append(parts, "Other Country")
			}
		} else {
			parts = append(parts, "Other Country")
		}
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// buildCountryMap builds a map of country code to MasterCountry for name resolution
func (s *buyerRegistrationRequest) buildCountryMap() map[string]entity.MasterCountry {
	countryByCode := map[string]entity.MasterCountry{}
	if countries, err := s.MasterCountryRepo.FindMasterCountryAll(); err == nil {
		for _, c := range countries {
			if c.CountryCode != nil {
				countryByCode[strings.ToUpper(*c.CountryCode)] = c
			}
		}
	}
	return countryByCode
}
