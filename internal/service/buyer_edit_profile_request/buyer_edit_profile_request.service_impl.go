package service

import (
	"backend-common-lib/constant"
	approvalStatusConstants "backend-common-lib/constants/edit_profile_approval_status"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	buyerRepository "content-service/internal/repository/buyer"
	buyerAddressRepository "content-service/internal/repository/buyer_address"
	buyerEditProfileRequestRepository "content-service/internal/repository/buyer_edit_profile_request"
	buyerEditProfileRequestCurrentRepository "content-service/internal/repository/buyer_edit_profile_request_current"
	buyerEditProfileRequestPastRepository "content-service/internal/repository/buyer_edit_profile_request_past"
	"fmt"
	"net/http"
	"reflect"
	"strings"

	"github.com/gofiber/fiber/v2/log"
	"gorm.io/gorm"
)

func (s *buyerEditprofileRequestService) FindBuyerEditProfileWithFilter(req dto.BuyerEditProfileSearchReqDto) (dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto], error) {
	responseDtos := dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto]{}

	results, count, err := s.findBuyerEditProfileWithFilterData(req, false)
	if err != nil {
		log.Error(err)
		return responseDtos, err
	}

	responseDtos = dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto]{
		PagingModel: *util.MapPaginationResult(results, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) FindBuyerEditProfileWithFilterMasking(req dto.BuyerEditProfileSearchReqDto) (dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto], error) {
	responseDtos := dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto]{}

	results, count, err := s.findBuyerEditProfileWithFilterData(req, true)
	if err != nil {
		log.Error(err)
		return responseDtos, err
	}

	results = maskingList(results)

	responseDtos = dto.BuyerEditProfilePageRespDto[dto.BuyerEditProfileSearchRespDto]{
		PagingModel: *util.MapPaginationResult(results, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) findBuyerEditProfileWithFilterData(req dto.BuyerEditProfileSearchReqDto, isMasking bool) ([]dto.BuyerEditProfileSearchRespDto, int64, error) {

	var results []dto.BuyerEditProfileSearchRespDto

	if req.ApprovalStatus != nil {
		upper := strings.ToUpper(util.Val(req.ApprovalStatus))
		req.ApprovalStatus = &upper
	}

	buyerEditProfileRequestDbs, err := s.BuyerEditProfileRequestRepo.FindBuyerEditProfileWithFilter(req)
	if err != nil {
		log.Error(err)
		return results, 0, errs.NewError(http.StatusInternalServerError, err)
	}

	count, err := s.BuyerEditProfileRequestRepo.CountBuyerEditProfileWithFilter(req)
	if err != nil {
		log.Error(err)
		return results, count, errs.NewError(http.StatusInternalServerError, err)
	}

	var buyerEditProfileRequestIds []int
	var buyerIds []int
	for _, n := range buyerEditProfileRequestDbs {
		buyerEditProfileRequestIds = append(buyerEditProfileRequestIds, n.Id)
		buyerIds = append(buyerIds, util.Val(n.BuyerId))
	}

	buyerDbs, err := s.BuyerRepo.GetAllByIds(buyerIds)
	if err != nil {
		log.Error(err)
		return results, count, errs.NewError(http.StatusInternalServerError, err)
	}

	buyerAddressDbs, err := s.BuyerAddressRepo.GetAllByIdsAndAddressType(buyerIds, constant.ADDRESS_TYPE_REGISTERED_ADDRESS)
	if err != nil {
		log.Error(err)
		return results, count, errs.NewError(http.StatusInternalServerError, err)
	}

	buyerPastDbs, err := s.BuyerEditProfileRequestRepo.GetAllBuyerPastByIds(buyerEditProfileRequestIds)
	if err != nil {
		log.Error(err)
		return results, count, errs.NewError(http.StatusInternalServerError, err)
	}

	buyerMap := map[int]*entity.Buyer{}
	for _, buyer := range buyerDbs {
		buyerMap[buyer.Id] = buyer
	}

	buyerAddressMap := map[int]*entity.BuyerAddress{}
	for _, buyerAddressDb := range buyerAddressDbs {
		buyerAddressMap[buyerAddressDb.BuyerID] = buyerAddressDb
	}

	buyerPastMap := map[int]*entity.BuyerEditProfileRequestPast{}
	for _, buyerPast := range buyerPastDbs {
		buyerPastMap[util.Val(buyerPast.BuyerEditProfileRequestId)] = buyerPast
	}

	layout := constant.DateFormatDMY

	for _, v := range buyerEditProfileRequestDbs {
		result := dto.BuyerEditProfileSearchRespDto{}

		editRequestDate := v.CreatedDate.Format(layout)
		result.BuyerId = v.BuyerId
		result.BuyerEditProfileRequestId = &v.Id
		result.CreatedDate = &editRequestDate

		old := dto.BuyerEditProfileSearchDto{}
		oldAddress := dto.BuyerEditProfileAddressDto{}
		new := dto.BuyerEditProfileSearchDto{}
		util.MapValue[entity.BuyerEditProfileRequest, dto.BuyerEditProfileSearchDto](&v, &new)
		newAddress := dto.BuyerEditProfileAddressDto{}
		util.MapValue[entity.BuyerEditProfileRequest, dto.BuyerEditProfileAddressDto](&v, &newAddress)
		if util.Val(v.ApprovalStatus) == approvalStatusConstants.EditProfileApprovalStatusWaiting {
			if buyerMap[util.Val(v.BuyerId)] != nil {
				old = dto.BuyerEditProfileSearchDto{}
				util.MapValue[entity.Buyer, dto.BuyerEditProfileSearchDto](buyerMap[util.Val(v.BuyerId)], &old)
			}
			if buyerAddressMap[util.Val(v.BuyerId)] != nil {
				oldAddress = dto.BuyerEditProfileAddressDto{}
				util.MapValue[entity.BuyerAddress, dto.BuyerEditProfileAddressDto](buyerAddressMap[util.Val(v.BuyerId)], &oldAddress)
			}

			old.PhoneNumber = buyerMap[util.Val(v.BuyerId)].PhoneNumber
			dateOfBirth := util.Val(buyerMap[util.Val(v.BuyerId)].DateOfBirth).Format(layout)
			old.DateOfBirthStr = &dateOfBirth
		} else {
			if buyerPastMap[v.Id] != nil {
				old = dto.BuyerEditProfileSearchDto{}
				util.MapValue[entity.BuyerEditProfileRequestPast, dto.BuyerEditProfileSearchDto](buyerPastMap[v.Id], &old)
				oldAddress = dto.BuyerEditProfileAddressDto{}
				util.MapValue[entity.BuyerEditProfileRequestPast, dto.BuyerEditProfileAddressDto](buyerPastMap[v.Id], &oldAddress)
			}

			old.PhoneNumber = buyerPastMap[v.Id].PhoneNumber
			dateOfBirth := util.Val(buyerPastMap[v.Id].DateOfBirth).Format(layout)
			old.DateOfBirthStr = &dateOfBirth
		}

		dateOfBirth := util.Val(v.DateOfBirth).Format(layout)
		new.DateOfBirthStr = &dateOfBirth

		addressNewTh, maskingAddressNewTh := AddressConcatTh(&newAddress)
		addressNewEn, maskingAddressNewEn := AddressConcatEn(&newAddress)

		addressOldTh, maskingAddressOldTh := AddressConcatTh(&oldAddress)
		addressOldEn, maskingAddressOldEn := AddressConcatEn(&oldAddress)

		if isMasking {
			new.AddressTh = &maskingAddressNewTh
			new.AddressEn = &maskingAddressNewEn
			old.AddressTh = &maskingAddressOldTh
			old.AddressEn = &maskingAddressOldEn
		} else {
			new.AddressTh = &addressNewTh
			new.AddressEn = &addressNewEn
			old.AddressTh = &addressOldTh
			old.AddressEn = &addressOldEn
		}

		changes := util.CompareStructDeep(old, new, "")

		mapChange := dto.BuyerEditProfileSearchChangeDto{}
		if len(changes) > 0 {
			mapChange = MapNewChangeList(old, new, changes)
		}

		result.Status = v.ApprovalStatus
		result.Remark = new.Remark
		result.BuyerEditProfileSearchChangeDto = mapChange

		results = append(results, result)
	}

	return results, count, nil
}

func (s *buyerEditprofileRequestService) UpdateBuyerEditProfileRequestStatus(req dto.UpdateBuyerEditProfileStatusReqDto) error {

	normalizedStatus, err := normalizeApprovalStatus(util.Val(req.ApprovalStatus))
	if err != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, err.Error(), "")
	}
	req.ApprovalStatus = &normalizedStatus

	if util.Val(req.ApprovalStatus) == string(approvalStatusConstants.EditProfileApprovalStatusWaiting) {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "approvalStatus is wrong", "error.buyerEditProfileRequest.wrongApprovalStatus")
	}

	if util.Val(req.ApprovalStatus) == string(approvalStatusConstants.EditProfileApprovalStatusRejected) && (req.Remark == nil || *req.Remark == "") {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "remark is required when rejecting", "")
	}

	buyerEditProfileRequestDb, err := s.BuyerEditProfileRequestRepo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	if util.Val(buyerEditProfileRequestDb.ApprovalStatus) != string(approvalStatusConstants.EditProfileApprovalStatusWaiting) {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "approvalStatus is already decided", "")
	}

	fieldsToUpdate := map[string]interface{}{
		"approval_status": req.ApprovalStatus,
		"remark":          req.Remark,
		"updated_by":      req.ActionBy,
		"updated_date":    util.Now(),
	}

	buyerDb := entity.Buyer{}
	buyerAddressDb := entity.BuyerAddress{}
	currentBuyer := entity.BuyerEditProfileRequestCurrent{}
	pastBuyer := entity.BuyerEditProfileRequestPast{}

	buyerDb, err = s.BuyerRepo.GetByIdWithPrefix(util.Val(buyerEditProfileRequestDb.BuyerId))
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	buyerAddressDb, err = s.BuyerAddressRepo.GetByIdAndAddressType(util.Val(buyerEditProfileRequestDb.BuyerId), constant.ADDRESS_TYPE_REGISTERED_ADDRESS)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	// new buyer and new address to BuyerEditProfileRequestCurrent
	currentBuyer = entity.BuyerEditProfileRequestCurrent{}
	util.MapValue[entity.BuyerEditProfileRequest, entity.BuyerEditProfileRequestCurrent](&buyerEditProfileRequestDb, &currentBuyer)
	currentBuyer.BuyerEditProfileRequestId = &buyerEditProfileRequestDb.Id
	currentBuyer.ApprovalStatus = req.ApprovalStatus
	currentBuyer.Remark = req.Remark
	currentBuyer.BaseEntity = &model.BaseEntity{
		CreatedBy:   req.ActionBy,
		CreatedDate: util.Now(),
		UpdatedBy:   req.ActionBy,
		UpdatedDate: util.NowPtr(),
	}

	// old buyer and old address to BuyerEditProfileRequestPast
	pastBuyer = entity.BuyerEditProfileRequestPast{}
	util.MapValue[entity.Buyer, entity.BuyerEditProfileRequestPast](&buyerDb, &pastBuyer)
	pastBuyer.Id = 0
	pastBuyer.HouseNumber = &buyerAddressDb.HouseNumber
	pastBuyer.RoomNumber = &buyerAddressDb.RoomNumber
	pastBuyer.Floor = &buyerAddressDb.Floor
	pastBuyer.Building = &buyerAddressDb.Building
	pastBuyer.Village = &buyerAddressDb.Village
	pastBuyer.Moo = &buyerAddressDb.Moo
	pastBuyer.Soi = &buyerAddressDb.Soi
	pastBuyer.Road = &buyerAddressDb.Road
	pastBuyer.PostCode = &buyerAddressDb.PostCode
	pastBuyer.SubDistrictId = &buyerAddressDb.SubDistrictID
	pastBuyer.DistrictId = &buyerAddressDb.DistrictID
	pastBuyer.ProvinceId = &buyerAddressDb.ProvinceID
	pastBuyer.CountryId = &buyerAddressDb.CountryID

	if util.Val(req.ApprovalStatus) == string(approvalStatusConstants.EditProfileApprovalStatusApproved) {
		// new buyer to Buyer
		buyerDb.BidderId = buyerEditProfileRequestDb.BidderId
		buyerDb.Email = buyerEditProfileRequestDb.Email
		buyerDb.PhoneNumber = buyerEditProfileRequestDb.PhoneNumber
		buyerDb.TaxId = buyerEditProfileRequestDb.TaxId //TODO - to delete
		buyerDb.IdentificationNumber = buyerEditProfileRequestDb.IdentificationNumber
		buyerDb.NationalityId = buyerEditProfileRequestDb.NationalityId
		buyerDb.DateOfBirth = buyerEditProfileRequestDb.DateOfBirth
		buyerDb.PrefixNameId = util.Val(buyerEditProfileRequestDb.PrefixNameId)
		buyerDb.FirstName = buyerEditProfileRequestDb.FirstName
		buyerDb.MiddleName = buyerEditProfileRequestDb.MiddleName
		buyerDb.LastName = buyerEditProfileRequestDb.LastName
		buyerDb.UpdatedBy = req.ActionBy
		buyerDb.UpdatedDate = util.NowPtr()

		// new buyer address to BuyerAddress
		buyerAddressDb.HouseNumber = util.Val(buyerEditProfileRequestDb.HouseNumber)
		buyerAddressDb.RoomNumber = util.Val(buyerEditProfileRequestDb.RoomNumber)
		buyerAddressDb.Floor = util.Val(buyerEditProfileRequestDb.Floor)
		buyerAddressDb.Building = util.Val(buyerEditProfileRequestDb.Building)
		buyerAddressDb.Village = util.Val(buyerEditProfileRequestDb.Village)
		buyerAddressDb.Moo = util.Val(buyerEditProfileRequestDb.Moo)
		buyerAddressDb.Soi = util.Val(buyerEditProfileRequestDb.Soi)
		buyerAddressDb.Road = util.Val(buyerEditProfileRequestDb.Road)
		buyerAddressDb.PostCode = util.Val(buyerEditProfileRequestDb.PostCode)
		buyerAddressDb.SubDistrictID = util.Val(buyerEditProfileRequestDb.SubDistrictId)
		buyerAddressDb.DistrictID = util.Val(buyerEditProfileRequestDb.DistrictId)
		buyerAddressDb.ProvinceID = util.Val(buyerEditProfileRequestDb.ProvinceId)
		buyerAddressDb.CountryID = util.Val(buyerEditProfileRequestDb.CountryId)
		buyerAddressDb.UpdatedBy = req.ActionBy
		buyerAddressDb.UpdatedDate = util.NowPtr()
	}

	return util.WithTx(s.BuyerEditProfileRequestRepo.GetDB(), func(tx *gorm.DB) error {
		buyerEditProfileRequestRepo := buyerEditProfileRequestRepository.NewBuyeEditProfileRequestRepository(tx)
		buyerRepo := buyerRepository.NewBuyerRepository(tx)
		buyerEditProfileRequestCurrentRepo := buyerEditProfileRequestCurrentRepository.NewBuyeEditProfileRequestCurrentRepository(tx)
		buyerEditProfileRequestPastRepo := buyerEditProfileRequestPastRepository.NewBuyeEditProfileRequestPastRepository(tx)
		buyerAddressRepo := buyerAddressRepository.NewBuyeAddressRepository(tx)

		err = buyerEditProfileRequestRepo.UpdateFields(req.Id, fieldsToUpdate)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		err = buyerEditProfileRequestCurrentRepo.Insert(currentBuyer)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		pastBuyer.BuyerEditProfileRequestCurrentId = &currentBuyer.Id
		err = buyerEditProfileRequestPastRepo.Insert(pastBuyer)
		if err != nil {
			log.Error(err)
			return errs.NewError(http.StatusInternalServerError, err)
		}

		if util.Val(req.ApprovalStatus) == string(approvalStatusConstants.EditProfileApprovalStatusApproved) {
			err = buyerRepo.UpdateAllFields(buyerDb)
			if err != nil {
				log.Error(err)
				return errs.NewError(http.StatusInternalServerError, err)
			}

			err = buyerAddressRepo.UpdateAllFields(buyerAddressDb)
			if err != nil {
				log.Error(err)
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil
	})
}

func (s *buyerEditprofileRequestService) GetBuyerEditProfileById(id int) (dto.BuyerEditProfileViewDto, error) {
	responseDtos, err := s.getBuyerEditProfileByIdData(id)
	if err != nil {
		return responseDtos, err
	}

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) GetBuyerEditProfileByIdMasking(id int) (dto.BuyerEditProfileViewDto, error) {
	responseDtos, err := s.getBuyerEditProfileByIdData(id)
	if err != nil {
		return responseDtos, err
	}

	responseDtos.OldBuyerEditProfileDto = maskingViewOld(responseDtos.OldBuyerEditProfileDto)
	responseDtos.NewBuyerEditProfileDto = maskingViewNew(responseDtos.NewBuyerEditProfileDto)

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) getBuyerEditProfileByIdData(id int) (dto.BuyerEditProfileViewDto, error) {
	responseDtos := dto.BuyerEditProfileViewDto{}

	buyerEditProfileRequestDb, err := s.BuyerEditProfileRequestRepo.GetById(id)
	if err != nil {
		log.Error(err)
		return responseDtos, errs.NewError(http.StatusInternalServerError, err)
	}

	buyerDb, err := s.BuyerRepo.GetByIdWithPrefix(util.Val(buyerEditProfileRequestDb.BuyerId))
	if err != nil {
		log.Error(err)
		return responseDtos, errs.NewError(http.StatusInternalServerError, err)
	}

	buyerAddressDb, err := s.BuyerAddressRepo.GetByIdAndAddressType(util.Val(buyerEditProfileRequestDb.BuyerId), constant.ADDRESS_TYPE_REGISTERED_ADDRESS)
	if err != nil {
		log.Error(err)
		return responseDtos, errs.NewError(http.StatusInternalServerError, err)
	}

	layout := constant.DateFormatDMY

	old := dto.BuyerEditProfileDto{}
	util.MapValue[entity.Buyer, dto.BuyerEditProfileDto](&buyerDb, &old)
	oldAddress := dto.BuyerEditProfileAddressDto{}
	util.MapValue[entity.BuyerAddress, dto.BuyerEditProfileAddressDto](&buyerAddressDb, &oldAddress)
	new := dto.BuyerEditProfileDto{}
	util.MapValue[entity.BuyerEditProfileRequest, dto.BuyerEditProfileDto](&buyerEditProfileRequestDb, &new)
	newAddress := dto.BuyerEditProfileAddressDto{}
	util.MapValue[entity.BuyerEditProfileRequest, dto.BuyerEditProfileAddressDto](&buyerEditProfileRequestDb, &newAddress)
	newAddress.HouseNumber = util.Val(buyerEditProfileRequestDb.HouseNumber)
	newAddress.RoomNumber = util.Val(buyerEditProfileRequestDb.RoomNumber)
	newAddress.Floor = util.Val(buyerEditProfileRequestDb.Floor)
	newAddress.Building = util.Val(buyerEditProfileRequestDb.Building)
	newAddress.Village = util.Val(buyerEditProfileRequestDb.Village)
	newAddress.Moo = util.Val(buyerEditProfileRequestDb.Moo)
	newAddress.Soi = util.Val(buyerEditProfileRequestDb.Soi)
	newAddress.Road = util.Val(buyerEditProfileRequestDb.Road)
	newAddress.PostCode = util.Val(buyerEditProfileRequestDb.PostCode)
	newAddress.SubDistrictID = util.Val(buyerEditProfileRequestDb.SubDistrictId)
	newAddress.DistrictID = util.Val(buyerEditProfileRequestDb.DistrictId)
	newAddress.ProvinceID = util.Val(buyerEditProfileRequestDb.ProvinceId)
	newAddress.CountryID = util.Val(buyerEditProfileRequestDb.CountryId)

	dateOfBirth := util.Val(buyerDb.DateOfBirth).Format(layout)
	old.DateOfBirthStr = &dateOfBirth
	dateOfBirth = util.Val(buyerEditProfileRequestDb.DateOfBirth).Format(layout)
	new.DateOfBirthStr = &dateOfBirth

	old.BuyerEditProfileAddressDto = &oldAddress
	new.BuyerEditProfileAddressDto = &newAddress

	changes := util.CompareStructDeep(old, new, "")
	addressChanges := util.CompareStructDeep(oldAddress, newAddress, "")

	newChange := dto.BuyerEditProfileChangeDto{}
	newAddressChange := dto.BuyerEditProfileAddressChangeDto{}
	newChange = MapNewChangeView(old, new, changes)
	newAddressChange = MapNewChangeAddressView(oldAddress, newAddress, addressChanges)

	newBuyerEditProfileDto := dto.BuyerEditProfileViewChangeDto{
		BuyerEditProfileChangeDto:        &newChange,
		BuyerEditProfileAddressChangeDto: &newAddressChange,
	}

	responseDtos.BuyerEditProfileRequestId = &buyerEditProfileRequestDb.Id
	responseDtos.BuyerId = buyerEditProfileRequestDb.BuyerId
	responseDtos.OldBuyerEditProfileDto = &old
	responseDtos.NewBuyerEditProfileDto = &newBuyerEditProfileDto

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) GetBuyerEditProfileLogById(id int) (dto.BuyerEditProfileViewDto, error) {
	responseDtos, err := s.getBuyerEditProfileByIdData(id)
	if err != nil {
		return responseDtos, err
	}

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) GetBuyerEditProfileLogByIdMasking(id int) (dto.BuyerEditProfileViewDto, error) {
	responseDtos, err := s.getBuyerEditProfileByIdData(id)
	if err != nil {
		return responseDtos, err
	}

	responseDtos.OldBuyerEditProfileDto = maskingViewOld(responseDtos.OldBuyerEditProfileDto)
	responseDtos.NewBuyerEditProfileDto = maskingViewNew(responseDtos.NewBuyerEditProfileDto)

	return responseDtos, nil
}

func (s *buyerEditprofileRequestService) getBuyerEditProfileLogByIdData(id int) (dto.BuyerEditProfileViewDto, error) {
	responseDtos := dto.BuyerEditProfileViewDto{}

	newBuyer, err := s.BuyerEditProfileRequestCurrentRepo.GetByBuyerEditProfileRequestId(id)
	if err != nil {
		log.Error(err)
		return responseDtos, errs.NewError(http.StatusInternalServerError, err)
	}

	oldBuyer, err := s.BuyerEditProfileRequestPastRepo.GetByBuyerEditProfileRequestCurrrentId(newBuyer.Id)
	if err != nil {
		log.Error(err)
		return responseDtos, errs.NewError(http.StatusInternalServerError, err)
	}

	layout := constant.DateFormatDMY

	old := dto.BuyerEditProfileDto{}
	util.MapValue[entity.BuyerEditProfileRequestPast, dto.BuyerEditProfileDto](&oldBuyer, &old)
	new := dto.BuyerEditProfileDto{}
	util.MapValue[entity.BuyerEditProfileRequestCurrent, dto.BuyerEditProfileDto](&newBuyer, &new)

	dateOfBirth := util.Val(oldBuyer.DateOfBirth).Format(layout)
	old.DateOfBirthStr = &dateOfBirth
	dateOfBirth = util.Val(newBuyer.DateOfBirth).Format(layout)
	new.DateOfBirthStr = &dateOfBirth

	changes := util.CompareStructDeep(old, new, "")

	newChange := dto.BuyerEditProfileViewChangeDto{}
	newChange = MapNewChangeLogView(old, new, changes)

	newBuyerEditProfileDto := newChange

	responseDtos.BuyerEditProfileRequestId = newBuyer.BuyerEditProfileRequestId
	responseDtos.BuyerId = newBuyer.BuyerId
	responseDtos.OldBuyerEditProfileDto = &old
	responseDtos.NewBuyerEditProfileDto = &newBuyerEditProfileDto

	return responseDtos, nil
}

func AddressConcatTh(buyerEditProfileRequest *dto.BuyerEditProfileAddressDto) (normalAddress string, maskingAddress string) {
	var result []string
	var houseNumber string
	var houseNumberMasking string

	if strings.TrimSpace(buyerEditProfileRequest.HouseNumber) != "" {
		houseNumber = fmt.Sprintf("บ้านเลขที่ %s", buyerEditProfileRequest.HouseNumber)
		masking := maskHouseNumber(buyerEditProfileRequest.HouseNumber)
		houseNumberMasking = fmt.Sprintf("บ้านเลขที่ %s", masking)
	}
	if strings.TrimSpace(buyerEditProfileRequest.RoomNumber) != "" {
		result = append(result, fmt.Sprintf("ห้อง %s", buyerEditProfileRequest.RoomNumber))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Floor) != "" {
		result = append(result, fmt.Sprintf("ชั้น %s", buyerEditProfileRequest.Floor))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Building) != "" {
		result = append(result, fmt.Sprintf("อาคาร %s", buyerEditProfileRequest.Building))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Village) != "" {
		result = append(result, fmt.Sprintf("หมู่บ้าน %s", buyerEditProfileRequest.Village))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Moo) != "" {
		result = append(result, fmt.Sprintf("หมู่ %s", buyerEditProfileRequest.Moo))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Soi) != "" {
		result = append(result, fmt.Sprintf("ซอย %s", buyerEditProfileRequest.Soi))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Road) != "" {
		result = append(result, fmt.Sprintf("ถนน %s", buyerEditProfileRequest.Road))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.SubDistrictDescriptionTh)) != "" {
		result = append(result, fmt.Sprintf("ตำบล%s", util.Val(buyerEditProfileRequest.SubDistrictDescriptionTh)))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.DistrictDescriptionTh)) != "" {
		result = append(result, fmt.Sprintf("อำเภอ%s", util.Val(buyerEditProfileRequest.DistrictDescriptionTh)))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.ProvinceDescriptionTh)) != "" {
		result = append(result, fmt.Sprintf("จังหวัด%s", util.Val(buyerEditProfileRequest.ProvinceDescriptionTh)))
	}
	if strings.TrimSpace(buyerEditProfileRequest.PostCode) != "" {
		result = append(result, fmt.Sprintf("%s", buyerEditProfileRequest.PostCode))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.CountryDescriptionTh)) != "" {
		result = append(result, fmt.Sprintf("ประเทศ%s", util.Val(buyerEditProfileRequest.CountryDescriptionTh)))
	}

	return fmt.Sprintf("%s %s", houseNumber, strings.Join(result, " ")), fmt.Sprintf("%s %s", houseNumberMasking, strings.Join(result, " "))
}

func AddressConcatEn(buyerEditProfileRequest *dto.BuyerEditProfileAddressDto) (normalAddress string, maskingAddress string) {
	var result []string
	var houseNumber string
	var houseNumberMasking string

	if strings.TrimSpace(buyerEditProfileRequest.HouseNumber) != "" {
		houseNumber = fmt.Sprintf("%s", buyerEditProfileRequest.HouseNumber)
		masking := maskHouseNumber(buyerEditProfileRequest.HouseNumber)
		houseNumberMasking = fmt.Sprintf("%s", masking)
	}
	if strings.TrimSpace(buyerEditProfileRequest.RoomNumber) != "" {
		result = append(result, fmt.Sprintf("Room %s", buyerEditProfileRequest.RoomNumber))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Floor) != "" {
		result = append(result, fmt.Sprintf("Floor %s", buyerEditProfileRequest.Floor))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Building) != "" {
		result = append(result, fmt.Sprintf("%s Building", buyerEditProfileRequest.Building))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Village) != "" {
		result = append(result, fmt.Sprintf("%s", buyerEditProfileRequest.Village))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Moo) != "" {
		result = append(result, fmt.Sprintf("Moo %s", buyerEditProfileRequest.Moo))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Soi) != "" {
		result = append(result, fmt.Sprintf("Soi %s", buyerEditProfileRequest.Soi))
	}
	if strings.TrimSpace(buyerEditProfileRequest.Road) != "" {
		result = append(result, fmt.Sprintf("%s Road", buyerEditProfileRequest.Road))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.SubDistrictDescriptionEn)) != "" {
		result = append(result, fmt.Sprintf("%s Sub-district", util.Val(buyerEditProfileRequest.SubDistrictDescriptionEn)))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.DistrictDescriptionEn)) != "" {
		result = append(result, fmt.Sprintf("%s District", util.Val(buyerEditProfileRequest.DistrictDescriptionEn)))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.ProvinceDescriptionEn)) != "" {
		result = append(result, fmt.Sprintf("%s", util.Val(buyerEditProfileRequest.ProvinceDescriptionEn)))
	}
	if strings.TrimSpace(buyerEditProfileRequest.PostCode) != "" {
		result = append(result, fmt.Sprintf("%s", buyerEditProfileRequest.PostCode))
	}
	if strings.TrimSpace(util.Val(buyerEditProfileRequest.CountryDescriptionEn)) != "" {
		result = append(result, fmt.Sprintf("%s", util.Val(buyerEditProfileRequest.CountryDescriptionEn)))
	}

	return fmt.Sprintf("%s %s", houseNumber, strings.Join(result, " ")), fmt.Sprintf("%s %s", houseNumberMasking, strings.Join(result, " "))
}

func MapNewChangeList(old interface{}, new interface{}, changes map[string]interface{}) dto.BuyerEditProfileSearchChangeDto {

	mapChange := dto.BuyerEditProfileSearchChangeDto{}

	srcVal := reflect.ValueOf(old)
	targetVal := reflect.ValueOf(new)
	mapChangeVal := reflect.ValueOf(&mapChange).Elem()

	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if targetVal.Kind() == reflect.Ptr {
		targetVal = targetVal.Elem()
	}

	srcType := srcVal.Type()

	for i := 0; i < srcVal.NumField(); i++ {
		fieldInfo := srcType.Field(i)
		fieldName := fieldInfo.Name

		lower := strings.ToLower(fieldName[:1]) + fieldName[1:]

		srcField := srcVal.Field(i)
		targetField := targetVal.Field(i)

		changeDto := &dto.ChangeValueDto{
			FieldName: &lower,
			IsEdit:    false,
			OldValue:  srcField.Interface(),
			NewValue:  targetField.Interface(),
		}

		if _, ok := changes[fieldName]; ok {
			changeDto.IsEdit = true
		}

		mapField := mapChangeVal.FieldByName(fieldName)
		if mapField.IsValid() && mapField.CanSet() {
			mapField.Set(reflect.ValueOf(changeDto))
		}

	}

	return mapChange
}

func MapNewChangeView(old interface{}, new interface{}, changes map[string]interface{}) dto.BuyerEditProfileChangeDto {

	mapChange := dto.BuyerEditProfileChangeDto{}

	srcVal := reflect.ValueOf(old)
	targetVal := reflect.ValueOf(new)
	mapChangeVal := reflect.ValueOf(&mapChange).Elem()

	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if targetVal.Kind() == reflect.Ptr {
		targetVal = targetVal.Elem()
	}

	srcType := srcVal.Type()

	for i := 0; i < srcVal.NumField(); i++ {
		fieldInfo := srcType.Field(i)
		fieldName := fieldInfo.Name

		lower := strings.ToLower(fieldName[:1]) + fieldName[1:]

		srcField := srcVal.Field(i)
		targetField := targetVal.Field(i)

		changeDto := &dto.ChangeValueDto{
			FieldName: &lower,
			IsEdit:    false,
			OldValue:  srcField.Interface(),
			NewValue:  targetField.Interface(),
		}

		if _, ok := changes[fieldName]; ok {
			changeDto.IsEdit = true
		}

		mapField := mapChangeVal.FieldByName(fieldName)
		if mapField.IsValid() && mapField.CanSet() {
			mapField.Set(reflect.ValueOf(changeDto))
		}

	}

	return mapChange
}

func MapNewChangeAddressView(old interface{}, new interface{}, changes map[string]interface{}) dto.BuyerEditProfileAddressChangeDto {

	mapChange := dto.BuyerEditProfileAddressChangeDto{}

	srcVal := reflect.ValueOf(old)
	targetVal := reflect.ValueOf(new)
	mapChangeVal := reflect.ValueOf(&mapChange).Elem()

	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if targetVal.Kind() == reflect.Ptr {
		targetVal = targetVal.Elem()
	}

	srcType := srcVal.Type()

	for i := 0; i < srcVal.NumField(); i++ {
		fieldInfo := srcType.Field(i)
		fieldName := fieldInfo.Name

		lower := strings.ToLower(fieldName[:1]) + fieldName[1:]

		srcField := srcVal.Field(i)
		targetField := targetVal.Field(i)

		changeDto := &dto.ChangeValueDto{
			FieldName: &lower,
			IsEdit:    false,
			OldValue:  srcField.Interface(),
			NewValue:  targetField.Interface(),
		}

		if _, ok := changes[fieldName]; ok {
			changeDto.IsEdit = true
		}

		mapField := mapChangeVal.FieldByName(fieldName)
		if mapField.IsValid() && mapField.CanSet() {
			mapField.Set(reflect.ValueOf(changeDto))
		}

	}

	return mapChange
}

func MapNewChangeLogView(old interface{}, new interface{}, changes map[string]interface{}) dto.BuyerEditProfileViewChangeDto {

	mapChange := dto.BuyerEditProfileViewChangeDto{}

	srcVal := reflect.ValueOf(old)
	targetVal := reflect.ValueOf(new)
	mapChangeVal := reflect.ValueOf(&mapChange).Elem()

	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if targetVal.Kind() == reflect.Ptr {
		targetVal = targetVal.Elem()
	}

	srcType := srcVal.Type()

	for i := 0; i < srcVal.NumField(); i++ {
		fieldInfo := srcType.Field(i)
		fieldName := fieldInfo.Name

		lower := strings.ToLower(fieldName[:1]) + fieldName[1:]

		srcField := srcVal.Field(i)
		targetField := targetVal.Field(i)

		changeDto := &dto.ChangeValueDto{
			FieldName: &lower,
			IsEdit:    false,
			OldValue:  srcField.Interface(),
			NewValue:  targetField.Interface(),
		}

		if _, ok := changes[fieldName]; ok {
			changeDto.IsEdit = true
		}

		mapField := mapChangeVal.FieldByName(fieldName)
		if mapField.IsValid() && mapField.CanSet() {
			mapField.Set(reflect.ValueOf(changeDto))
		}

	}

	return mapChange
}

func maskingList(results []dto.BuyerEditProfileSearchRespDto) []dto.BuyerEditProfileSearchRespDto {

	var masking interface{}
	for _, result := range results {
		if result.Email.IsEdit {
			masking = maskEmail(result.Email.OldValue)
			result.Email.OldValue = masking
			masking = maskEmail(result.Email.NewValue)
			result.Email.NewValue = masking
		} else {
			masking = maskEmail(result.Email.OldValue)
			result.Email.OldValue = masking
			result.Email.NewValue = masking
		}

		if result.PhoneNumber.IsEdit {
			masking = maskPhone(result.PhoneNumber.OldValue)
			result.PhoneNumber.OldValue = masking
			masking = maskPhone(result.PhoneNumber.NewValue)
			result.PhoneNumber.NewValue = masking
		} else {
			masking = maskPhone(result.PhoneNumber.OldValue)
			result.PhoneNumber.OldValue = masking
			result.PhoneNumber.NewValue = masking
		}

		if result.FirstName.IsEdit {
			masking = maskName(result.FirstName.OldValue)
			result.FirstName.OldValue = masking
			masking = maskName(result.FirstName.NewValue)
			result.FirstName.NewValue = masking
		} else {
			masking = maskName(result.FirstName.OldValue)
			result.FirstName.OldValue = masking
			result.FirstName.NewValue = masking
		}

		if result.MiddleName.IsEdit {
			masking = maskName(result.MiddleName.OldValue)
			result.MiddleName.OldValue = masking
			masking = maskName(result.MiddleName.NewValue)
			result.MiddleName.NewValue = masking
		} else {
			masking = maskName(result.MiddleName.OldValue)
			result.MiddleName.OldValue = masking
			result.MiddleName.NewValue = masking
		}

		if result.LastName.IsEdit {
			masking = maskName(result.LastName.OldValue)
			result.LastName.OldValue = masking
			masking = maskName(result.LastName.NewValue)
			result.LastName.NewValue = masking
		} else {
			masking = maskName(result.LastName.OldValue)
			result.LastName.OldValue = masking
			result.LastName.NewValue = masking
		}

		if result.IdentificationNumber.IsEdit {
			masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber.OldValue)
			result.IdentificationNumber.OldValue = masking
			masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber.NewValue)
			result.IdentificationNumber.NewValue = masking
		} else {
			masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber.OldValue)
			result.IdentificationNumber.OldValue = masking
			result.IdentificationNumber.NewValue = masking
		}

		if result.DateOfBirthStr.IsEdit {
			masking = maskDateYear(result.DateOfBirthStr.OldValue)
			result.DateOfBirthStr.OldValue = masking
			masking = maskDateYear(result.DateOfBirthStr.NewValue)
			result.DateOfBirthStr.NewValue = masking
		} else {
			masking = maskDateYear(result.DateOfBirthStr.OldValue)
			result.DateOfBirthStr.OldValue = masking
			result.DateOfBirthStr.NewValue = masking
		}
	}

	//address

	return results
}

func maskingViewOld(result *dto.BuyerEditProfileDto) *dto.BuyerEditProfileDto {

	var masking interface{}

	masking = maskEmail(result.Email)
	email := toString(masking)
	result.Email = &email

	masking = maskPhone(result.PhoneNumber)
	result.PhoneNumber = toString(masking)

	masking = maskName(result.FirstName)
	firstName := toString(masking)
	result.FirstName = &firstName

	masking = maskName(result.MiddleName)
	middleName := toString(masking)
	result.MiddleName = &middleName

	masking = maskName(result.LastName)
	lastName := toString(masking)
	result.LastName = &lastName

	masking = maskIdentificationNumberAndTaxId(result.TaxId)
	taxId := toString(masking)
	result.TaxId = &taxId

	masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber)
	identificationNumber := toString(masking)
	result.IdentificationNumber = &identificationNumber

	masking = maskDateYear(result.DateOfBirthStr)
	dateOfBirthStr := toString(masking)
	result.DateOfBirthStr = &dateOfBirthStr

	masking = maskHouseNumber(result.HouseNumber)
	result.HouseNumber = toString(masking)

	return result
}

func maskingViewNew(result *dto.BuyerEditProfileViewChangeDto) *dto.BuyerEditProfileViewChangeDto {

	var masking interface{}
	if result.Email.IsEdit {
		masking = maskEmail(result.Email.OldValue)
		result.Email.OldValue = masking
		masking = maskEmail(result.Email.NewValue)
		result.Email.NewValue = masking
	} else {
		masking = maskEmail(result.Email.OldValue)
		result.Email.OldValue = masking
		result.Email.NewValue = masking
	}

	if result.PhoneNumber.IsEdit {
		masking = maskPhone(result.PhoneNumber.OldValue)
		result.PhoneNumber.OldValue = masking
		masking = maskPhone(result.PhoneNumber.NewValue)
		result.PhoneNumber.NewValue = masking
	} else {
		masking = maskPhone(result.PhoneNumber.OldValue)
		result.PhoneNumber.OldValue = masking
		result.PhoneNumber.NewValue = masking
	}

	if result.FirstName.IsEdit {
		masking = maskName(result.FirstName.OldValue)
		result.FirstName.OldValue = masking
		masking = maskName(result.FirstName.NewValue)
		result.FirstName.NewValue = masking
	} else {
		masking = maskName(result.FirstName.OldValue)
		result.FirstName.OldValue = masking
		result.FirstName.NewValue = masking
	}

	if result.MiddleName.IsEdit {
		masking = maskName(result.MiddleName.OldValue)
		result.MiddleName.OldValue = masking
		masking = maskName(result.MiddleName.NewValue)
		result.MiddleName.NewValue = masking
	} else {
		masking = maskName(result.MiddleName.OldValue)
		result.MiddleName.OldValue = masking
		result.MiddleName.NewValue = masking
	}

	if result.LastName.IsEdit {
		masking = maskName(result.LastName.OldValue)
		result.LastName.OldValue = masking
		masking = maskName(result.LastName.NewValue)
		result.LastName.NewValue = masking
	} else {
		masking = maskName(result.LastName.OldValue)
		result.LastName.OldValue = masking
		result.LastName.NewValue = masking
	}

	if result.TaxId.IsEdit {
		masking = maskIdentificationNumberAndTaxId(result.TaxId.OldValue)
		result.TaxId.OldValue = masking
		masking = maskIdentificationNumberAndTaxId(result.TaxId.NewValue)
		result.TaxId.NewValue = masking
	} else {
		masking = maskIdentificationNumberAndTaxId(result.TaxId.OldValue)
		result.TaxId.OldValue = masking
		result.TaxId.NewValue = masking
	}

	if result.IdentificationNumber.IsEdit {
		masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber.OldValue)
		result.IdentificationNumber.OldValue = masking
		masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber.NewValue)
		result.IdentificationNumber.NewValue = masking
	} else {
		masking = maskIdentificationNumberAndTaxId(result.IdentificationNumber.OldValue)
		result.IdentificationNumber.OldValue = masking
		result.IdentificationNumber.NewValue = masking
	}

	if result.DateOfBirthStr.IsEdit {
		masking = maskDateYear(result.DateOfBirthStr.OldValue)
		result.DateOfBirthStr.OldValue = masking
		masking = maskDateYear(result.DateOfBirthStr.NewValue)
		result.DateOfBirthStr.NewValue = masking
	} else {
		masking = maskDateYear(result.DateOfBirthStr.OldValue)
		result.DateOfBirthStr.OldValue = masking
		result.DateOfBirthStr.NewValue = masking
	}

	if result.HouseNumber.IsEdit {
		masking = maskHouseNumber(result.HouseNumber.OldValue)
		result.HouseNumber.OldValue = masking
		masking = maskHouseNumber(result.HouseNumber.NewValue)
		result.HouseNumber.NewValue = masking
	} else {
		masking = maskHouseNumber(result.HouseNumber.OldValue)
		result.HouseNumber.OldValue = masking
		result.HouseNumber.NewValue = masking
	}

	return result
}

func toString(input interface{}) string {
	switch v := input.(type) {
	case string:
		return v
	case *string:
		return util.Val(v)
	default:
		return ""
	}
}

func maskEmail(input interface{}) interface{} {
	str := toString(input)

	parts := strings.Split(str, "@")
	if len(parts) != 2 {
		return str
	}

	name := parts[0]
	domain := parts[1]

	if len(name) < 2 {
		return str
	}

	masked := string(name[0]) + strings.Repeat("*", len(name)-1)

	return masked + "@" + domain
}

func maskPhone(input interface{}) interface{} {
	str := toString(input)

	if len(str) > 6 {
		front := str[:3]
		back := str[len(str)-2:]
		masked := strings.Repeat("*", len(str)-5)

		return front + masked + back
	} else if len(str) > 4 {
		front := str[:2]
		back := str[len(str)-2:]
		masked := strings.Repeat("*", len(str)-4)

		return front + masked + back
	}

	return str
}

func maskName(input interface{}) interface{} {
	str := toString(input)
	runes := []rune(str)

	if len(runes) < 3 {
		return str
	}

	prefix := string(runes[:2])
	masked := strings.Repeat("*", len(runes)-2)
	return prefix + masked
}

func maskIdentificationNumberAndTaxId(input interface{}) interface{} {
	str := toString(input)

	if len(str) > 6 {
		front := str[:3]
		back := str[len(str)-3:]
		masked := strings.Repeat("*", len(str)-6)

		return front + masked + back
	} else if len(str) > 4 {
		front := str[:2]
		back := str[len(str)-2:]
		masked := strings.Repeat("*", len(str)-4)

		return front + masked + back
	}

	return str
}

func maskDateYear(input interface{}) interface{} {
	str := toString(input)

	parts := strings.Split(str, "/")
	if len(parts) != 3 {
		return str
	}

	return fmt.Sprintf("%s/%s/****", parts[0], parts[1])
}

func maskHouseNumber(input interface{}) string {
	str := toString(input)

	parts := strings.Split(str, "/")
	var maskedParts []string

	for _, part := range parts {
		if len(part) == 0 {
			maskedParts = append(maskedParts, "")
			continue
		}
		runes := []rune(part)

		firstChar := string(runes[:1])
		mask := strings.Repeat("*", len(part)-1)
		maskedParts = append(maskedParts, firstChar+mask)
	}

	return strings.Join(maskedParts, "/")
}

func normalizeApprovalStatus(status string) (string, error) {
	// Convert to uppercase
	upperStatus := strings.ToUpper(status)

	switch upperStatus {
	case "APPROVED":
		return approvalStatusConstants.EditProfileApprovalStatusApproved, nil
	case "REJECTED":
		return approvalStatusConstants.EditProfileApprovalStatusRejected, nil
	default:
		return "", fmt.Errorf("invalid approval status: %s. Only 'approved' or 'rejected' are allowed", status)
	}
}
